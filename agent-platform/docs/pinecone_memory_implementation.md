# Pinecone Memory Implementation for AutoGen Agents

This document describes the comprehensive implementation of Pinecone-based memory for AutoGen agents, providing persistent, searchable, and scalable memory capabilities.

## Overview

The Pinecone memory implementation replaces the default ListMemory with a vector database-backed memory system that provides:

- **Persistent Memory**: Agent memories survive across sessions and restarts
- **Semantic Search**: Vector-based similarity search for relevant memory retrieval
- **Scalable Storage**: Cloud-based vector database with unlimited capacity
- **Multi-tenant Support**: Isolated memories per agent and user
- **Knowledge Management**: Structured storage and retrieval of agent knowledge

## Architecture Components

### 1. Pinecone Memory (`app/memory/pinecone_memory.py`)

**Purpose**: Core implementation of AutoGen's Memory protocol using Pinecone.

**Key Features**:

- Implements AutoGen's `Memory` interface
- Vector embeddings using OpenAI's embedding models
- Namespace-based memory isolation
- Automatic index management
- Similarity-based memory retrieval

**Key Methods**:

```python
async def add(memory: MemoryContent) -> None
async def query(query: str, k: Optional[int] = None) -> List[MemoryContent]
async def update_context(context: ChatCompletionContext) -> None
async def clear() -> None
```

### 2. Pinecone Memory Manager (`app/memory/pinecone_memory_manager.py`)

**Purpose**: High-level management interface for agent memories.

**Key Features**:

- Agent memory instance management
- Knowledge storage and retrieval
- Conversation memory persistence
- Memory transfer between agents
- Statistics and monitoring

**Key Methods**:

```python
def get_agent_memory(agent_id: str, user_id: Optional[str] = None) -> PineconeMemory
async def add_agent_knowledge(agent_id: str, knowledge_items: List[Dict]) -> int
async def add_conversation_memory(agent_id: str, user_message: str, agent_response: str) -> bool
async def query_agent_memory(agent_id: str, query: str) -> List[MemoryContent]
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Pinecone Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=agent-memory
PINECONE_DIMENSION=1536
PINECONE_METRIC=cosine
PINECONE_CLOUD=aws
```

### Settings Configuration

The configuration is automatically loaded through the settings system:

```python
@dataclass
class PineconeConfig:
    api_key: str
    environment: str = "us-east-1"
    index_name: str = "agent-memory"
    dimension: int = 1536  # Default for OpenAI embeddings
    metric: str = "cosine"
    cloud: str = "aws"
```

## Integration with Agents

### Agent Factory Integration

The `AgentFactory` has been updated to support Pinecone memory:

```python
async def create_agent(
    self,
    run_id: str,
    agent_config: AgentConfig,
    model_context: BufferedChatCompletionContext,
    memory: Optional[ListMemory] = None,
    organization_id: Optional[str] = None,
    use_knowledge: Optional[bool] = False,
    variables: Optional[dict] = None,
    user_id: Optional[str] = None,
    use_pinecone_memory: bool = True,  # New parameter
) -> AssistantAgent:
```

**Memory Selection Logic**:

```python
if use_pinecone_memory:
    # Use Pinecone memory for persistent, searchable memory
    pinecone_memory = memory_manager.get_agent_memory(
        agent_id=agent_config.id or agent_config.name,
        user_id=user_id,
        namespace=f"agent-{agent_config.id or agent_config.name}",
    )
    agent_memory.append(pinecone_memory)
elif memory:
    # Use provided ListMemory
    agent_memory.append(memory)
```

### Group Chat Integration

Group chat agents automatically use Pinecone memory:

```python
agent = await self.agent_factory.create_agent(
    run_id=session_id,
    agent_config=agent_config,
    model_context=model_context,
    memory=None,  # Will use Pinecone memory
    organization_id=organization_id,
    use_knowledge=use_knowledge,
    variables=variables,
    user_id=user_id,
    use_pinecone_memory=True,
)
```

## Memory Types and Organization

### Namespace Structure

Memories are organized using namespaces:

- **Agent-specific**: `agent-{agent_id}`
- **User-specific**: `agent-{agent_id}-user-{user_id}`
- **Custom**: User-defined namespaces

### Memory Types

1. **Knowledge Memory**:

   ```python
   {
       "content": "Python is a high-level programming language...",
       "metadata": {
           "knowledge_type": "technical_knowledge",
           "topic": "programming",
           "added_at": "2024-01-15T10:30:00Z",
           "source": "knowledge_base"
       }
   }
   ```

2. **Conversation Memory**:
   ```python
   {
       "content": "User: What is Python?\nAgent: Python is a programming language...",
       "metadata": {
           "type": "conversation",
           "conversation_id": "conv-123",
           "timestamp": "2024-01-15T10:30:00Z",
           "user_id": "user-456"
       }
   }
   ```

## Usage Examples

### Basic Memory Operations

```python
from app.memory import memory_manager

# Get agent memory
memory = memory_manager.get_agent_memory(
    agent_id="agent-123",
    user_id="user-456"
)

# Add knowledge
knowledge_items = [
    {
        "content": "FastAPI is a modern web framework for Python",
        "metadata": {"topic": "web_development", "framework": "fastapi"}
    }
]

await memory_manager.add_agent_knowledge(
    agent_id="agent-123",
    knowledge_items=knowledge_items,
    knowledge_type="technical_knowledge"
)

# Query memory
results = await memory_manager.query_agent_memory(
    agent_id="agent-123",
    query="web framework",
    k=5
)
```

### Agent Creation with Pinecone Memory

```python
from app.autogen_service.agent_factory import AgentFactory
from app.schemas.api import AgentConfig

agent_factory = AgentFactory()

agent_config = AgentConfig(
    id="tech-assistant",
    name="TechnicalAssistant",
    description="AI assistant with technical knowledge",
    system_message="You are a technical AI assistant with persistent memory.",
    model_name="gpt-4o-mini"
)

agent = await agent_factory.create_agent(
    run_id="session-123",
    agent_config=agent_config,
    model_context=model_context,
    user_id="user-456",
    use_pinecone_memory=True,  # Enable Pinecone memory
    use_knowledge=True,
    organization_id="org-789"
)
```

### Memory-Enhanced Conversations

```python
# Add conversation to memory
await memory_manager.add_conversation_memory(
    agent_id="agent-123",
    user_message="How do I create a FastAPI application?",
    agent_response="To create a FastAPI application, you start by importing FastAPI...",
    user_id="user-456",
    conversation_id="conv-789"
)

# Query for relevant context
context_memories = await memory_manager.query_agent_memory(
    agent_id="agent-123",
    query="FastAPI application development",
    user_id="user-456",
    k=3
)
```

## Advanced Features

### Memory Transfer Between Agents

```python
# Transfer knowledge from expert to apprentice
transferred_count = await memory_manager.transfer_knowledge_between_agents(
    source_agent_id="expert-agent",
    target_agent_id="apprentice-agent",
    query="machine learning concepts",
    user_id="user-456",
    k=10
)
```

### Memory Statistics and Monitoring

```python
# Get memory statistics
stats = await memory_manager.get_agent_memory_stats(
    agent_id="agent-123",
    user_id="user-456"
)

print(f"Total vectors: {stats['total_vectors']}")
print(f"Agent vectors: {stats['namespace_vectors']}")
print(f"Index fullness: {stats['index_fullness']}")
```

### Memory Cleanup

```python
# Clear specific agent memory
await memory_manager.clear_agent_memory(
    agent_id="agent-123",
    user_id="user-456"
)

# Close all memory instances
await memory_manager.close_all_memories()
```

## Performance Considerations

### Embedding Optimization

- Uses OpenAI's `text-embedding-3-small` model by default
- Configurable embedding models for different use cases
- Batch processing for multiple memory additions

### Vector Search Optimization

- Cosine similarity for semantic search
- Configurable similarity thresholds
- Namespace filtering for efficient queries

### Scalability Features

- Serverless Pinecone deployment
- Automatic index scaling
- Multi-region support

## Testing

### Comprehensive Test Suite

Run the Pinecone memory integration tests:

```bash
cd tests
python test_pinecone_memory_integration.py
```

### Test Coverage

The test suite covers:

1. **Basic Operations**: Memory creation, knowledge addition, retrieval
2. **Agent Integration**: Agent creation with Pinecone memory
3. **Persistence**: Cross-session memory retrieval
4. **Knowledge Transfer**: Memory sharing between agents
5. **Cleanup**: Memory management and cleanup

## Monitoring and Debugging

### Logging

Comprehensive logging throughout the memory system:

```python
logger.info(f"Added {count} knowledge items to agent {agent_id}")
logger.debug(f"Retrieved {len(results)} memories for query: {query}")
logger.error(f"Error adding memory: {error}")
```

### Memory Inspection

```python
# Get detailed memory information
memory_info = await memory.get_stats()
print(f"Memory stats: {memory_info}")

# Query with detailed results
results = await memory.query("query", k=5)
for result in results:
    print(f"Score: {result.metadata.get('score')}")
    print(f"Content: {result.content}")
    print(f"Metadata: {result.metadata}")
```

## Migration from ListMemory

### Gradual Migration

1. **Phase 1**: Deploy Pinecone memory alongside ListMemory
2. **Phase 2**: Enable Pinecone memory for new agents
3. **Phase 3**: Migrate existing agents to Pinecone memory
4. **Phase 4**: Remove ListMemory dependency

### Data Migration

```python
# Example migration script
async def migrate_agent_memory(agent_id: str, old_memory: ListMemory):
    pinecone_memory = memory_manager.get_agent_memory(agent_id)

    # Extract memories from ListMemory
    old_memories = await old_memory.get_all()  # Hypothetical method

    # Convert and add to Pinecone
    for memory_content in old_memories:
        await pinecone_memory.add(memory_content)
```

## Troubleshooting

### Common Issues

1. **Index Not Found**: Ensure Pinecone index exists and is accessible
2. **API Key Issues**: Verify Pinecone API key is valid and has permissions
3. **Embedding Errors**: Check OpenAI API key and model availability
4. **Namespace Conflicts**: Use unique namespaces for different agents/users

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger("app.memory").setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Features

1. **Hybrid Search**: Combine vector and keyword search
2. **Memory Compression**: Automatic summarization of old memories
3. **Multi-modal Memory**: Support for images and documents
4. **Memory Analytics**: Advanced analytics and insights
5. **Custom Embeddings**: Support for domain-specific embedding models
