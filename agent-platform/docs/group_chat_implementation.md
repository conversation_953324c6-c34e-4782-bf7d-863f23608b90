# Agent Group Chat Implementation

This document describes the comprehensive implementation of agent group chat functionality using AutoGen's team-based conversation patterns.

## Overview

The group chat implementation provides a complete flow for creating and managing multi-agent conversations based on predefined agent groups. It supports different team types (RoundR<PERSON><PERSON>, Selector) and integrates seamlessly with the existing agent platform infrastructure.

## Architecture Components

### 1. Agent Group Service (`app/services/agent_group_service.py`)

**Purpose**: Manages agent groups and their configurations.

**Key Features**:
- Fetch group details and member agents from the API
- Validate groups for chat readiness
- Get group chat configurations
- Convert agent data to proper `AgentConfig` format

**Key Methods**:
```python
async def fetch_group_details(group_id: str) -> Optional[Dict[str, Any]]
async def fetch_group_agents(group_id: str) -> List[Dict[str, Any]]
async def validate_group_for_chat(group_id: str) -> bool
async def get_group_chat_config(group_id: str) -> Optional[Dict[str, Any]]
```

### 2. Group Chat Factory (`app/autogen_service/group_chat_factory.py`)

**Purpose**: Creates different types of AutoGen team configurations.

**Supported Team Types**:
- **RoundRobinGroupChat**: Agents take turns in a round-robin fashion
- **SelectorGroupChat**: Uses an LLM to select the next speaker

**Key Features**:
- Dynamic team creation based on group configuration
- Flexible termination conditions
- Support for custom chat configurations

**Key Methods**:
```python
async def create_group_chat_team(
    group_id: str,
    agents: List[AssistantAgent],
    chat_config: Optional[Dict[str, Any]] = None
) -> Optional[Union[RoundRobinGroupChat, SelectorGroupChat]]
```

### 3. Group Chat Processor (`app/autogen_service/group_chat_processor.py`)

**Purpose**: Handles group chat conversations and session management.

**Key Features**:
- Create and manage group chat sessions
- Process group chat messages with streaming responses
- Session state management
- Integration with knowledge tools and variables

**Key Methods**:
```python
async def create_group_chat_session(
    group_id: str,
    user_id: str,
    communication_type: str = "group_chat",
    organization_id: Optional[str] = None,
    use_knowledge: Optional[bool] = False,
    variables: Optional[dict] = None,
) -> Optional[str]

async def process_group_chat(
    session_id: str,
    user_message: str,
    run_id: Optional[str] = None,
) -> AsyncGenerator[dict, None]
```

## Complete Flow Implementation

### 1. Group Chat Session Creation

```mermaid
sequenceDiagram
    participant User
    participant API
    participant GroupChatProcessor
    participant AgentGroupService
    participant AgentFactory
    participant SessionManager

    User->>API: POST /group-chat/sessions
    API->>GroupChatProcessor: create_group_chat_session()
    GroupChatProcessor->>AgentGroupService: validate_group_for_chat()
    GroupChatProcessor->>AgentGroupService: get_group_agent_configs()
    GroupChatProcessor->>AgentFactory: create_agent() (for each agent)
    GroupChatProcessor->>GroupChatFactory: create_group_chat_team()
    GroupChatProcessor->>SessionManager: create_session()
    GroupChatProcessor-->>API: session_id
    API-->>User: {"session_id": "...", "success": true}
```

### 2. Group Chat Message Processing

```mermaid
sequenceDiagram
    participant User
    participant API
    participant GroupChatProcessor
    participant AutoGenTeam
    participant SessionManager

    User->>API: POST /group-chat/sessions/{id}/messages
    API->>GroupChatProcessor: process_group_chat()
    GroupChatProcessor->>SessionManager: get_session_data()
    GroupChatProcessor->>AutoGenTeam: run_stream()
    loop For each message
        AutoGenTeam-->>GroupChatProcessor: agent_message
        GroupChatProcessor-->>API: response_chunk
        API-->>User: SSE: agent_message
    end
    AutoGenTeam-->>GroupChatProcessor: TaskResult (final)
    GroupChatProcessor-->>API: final_response
    API-->>User: SSE: [DONE]
```

## Kafka Integration

### Agent Creation with Group Support

The Kafka consumer now handles group chat requests:

```python
# In process_agent_creation()
if request.agent_group_id:
    # Create group chat session
    session_id = await self.group_chat_processor.create_group_chat_session(
        group_id=request.agent_group_id,
        user_id=request.user_id,
        communication_type=request.communication_type,
        organization_id=request.organization_id,
        use_knowledge=request.use_knowledge,
        variables=request.variables,
    )
else:
    # Single agent session (existing logic)
    # ...
```

### Chat Processing with Group Support

```python
# In process_agent_chat()
group_chat_info = await self.group_chat_processor.get_group_chat_info(request.session_id)

if group_chat_info:
    # Process as group chat
    async for response_chunk in self.group_chat_processor.process_group_chat(...):
        # Stream responses via Kafka
        # ...
else:
    # Process as single agent chat (existing logic)
    # ...
```

## API Endpoints

### Group Chat Management

1. **Create Group Chat Session**
   ```
   POST /group-chat/sessions
   Body: {
     "group_id": "group-123",
     "user_id": "user-456",
     "organization_id": "org-789",
     "use_knowledge": true,
     "variables": {"key": "value"}
   }
   ```

2. **Send Message to Group Chat**
   ```
   POST /group-chat/sessions/{session_id}/messages
   Body: {
     "message": "Hello team, can you help me with this task?",
     "run_id": "run-123"
   }
   Response: Server-Sent Events stream
   ```

3. **Get Group Chat Info**
   ```
   GET /group-chat/sessions/{session_id}/info
   ```

4. **End Group Chat Session**
   ```
   DELETE /group-chat/sessions/{session_id}
   ```

### Group Validation

1. **Validate Group for Chat**
   ```
   GET /group-chat/groups/{group_id}/validate
   ```

2. **Get Group Details**
   ```
   GET /group-chat/groups/{group_id}/details
   ```

## Configuration

### Group Chat Configuration

Groups can be configured with the following settings:

```json
{
  "team_type": "round_robin",  // or "selector"
  "max_messages": 10,
  "termination_keywords": ["TERMINATE", "STOP", "END"],
  "selector_model": "gpt-4o-mini",
  "group_description": "Development team for code review",
  "group_name": "Code Review Team"
}
```

### Termination Conditions

The system supports multiple termination conditions:

- **MaxMessageTermination**: Stop after a maximum number of messages
- **TextMentionTermination**: Stop when specific keywords are mentioned
- **ExternalTermination**: Manual termination from outside

## Knowledge Integration

Group chats support knowledge tools when `use_knowledge=True`:

- Each agent in the group gets access to knowledge tools
- Organization-scoped knowledge access
- Google Drive API integration for knowledge retrieval

## Variable Substitution

Dynamic prompt variables are supported:

```python
variables = {
    "project_name": "MyProject",
    "deadline": "2024-01-15",
    "priority": "high"
}

# In agent system messages:
# "You are working on {{project_name}} with deadline {{deadline}} and {{priority}} priority."
```

## Error Handling

Comprehensive error handling throughout the flow:

- Group validation errors
- Agent creation failures
- Team creation issues
- Session management errors
- Communication failures

## Testing

### Test Script

Run the comprehensive test:

```bash
cd tests
python test_group_chat_flow.py
```

### Test Coverage

The test script covers:

1. **Group Validation**: Verify group exists and is ready for chat
2. **Session Creation**: Create group chat session successfully
3. **Conversation Flow**: Send messages and receive responses
4. **Session Cleanup**: Properly end sessions and cleanup resources

## Performance Considerations

### Optimizations

1. **Session Caching**: Active group chats are cached in memory
2. **Concurrent Processing**: Multiple group chats can run simultaneously
3. **Resource Management**: Proper cleanup of agents and teams
4. **Memory Management**: Session TTL and message limits

### Scalability

- Redis-based session storage for horizontal scaling
- Kafka for distributed message processing
- Stateless API design for load balancing

## Monitoring and Logging

Comprehensive logging throughout the system:

- Group validation events
- Session creation/destruction
- Message processing
- Error conditions
- Performance metrics

## Future Enhancements

Potential improvements:

1. **Advanced Team Types**: Support for more AutoGen team patterns
2. **Dynamic Agent Addition**: Add/remove agents during conversation
3. **Conversation Branching**: Support for parallel conversation threads
4. **Advanced Analytics**: Conversation analysis and insights
5. **Custom Termination**: User-defined termination conditions
6. **Agent Roles**: Specialized roles within teams (moderator, specialist, etc.)

## Troubleshooting

Common issues and solutions:

1. **Group Not Found**: Verify group ID exists in the system
2. **No Agents in Group**: Ensure group has at least one active agent
3. **Session Creation Fails**: Check agent configurations and API connectivity
4. **Team Creation Errors**: Verify model client configurations
5. **Knowledge Tool Issues**: Check organization ID and API keys
