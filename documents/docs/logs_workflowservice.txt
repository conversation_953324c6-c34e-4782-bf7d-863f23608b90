one, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'video_type', 'display_name': 'video type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'SHORT', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'link', 'display_name': 'Link', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'title', 'display_name': 'Title', 'output_type': 'string'}, {'name': 'script', 'display_name': 'Script', 'output_type': 'string'}, {'name': 'script_type', 'display_name': 'Script Type', 'output_type': 'string'}, {'name': 'video_type', 'display_name': 'Video Type', 'output_type': 'string'}, {'name': 'link', 'display_name': 'Link', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_script_generation_script_generate', 'type': 'MCP', 'mcp_info': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}, 'component_config': {}, 'mcp_metadata': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}]}}, 'style': {'opacity': 1}, 'width': 208, 'height': 218, 'selected': False, 'positionAbsolute': {'x': 520, 'y': -160}, 'dragging': False}, {'id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423', 'type': 'WorkflowNode', 'position': {'x': -160, 'y': -200}, 'data': {'label': 'Tavily Web Search and Extraction Server - tavily-search', 'type': 'mcp', 'originalType': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'definition': {'name': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'display_name': 'Tavily Web Search and Extraction Server - tavily-search', 'description': "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'query', 'display_name': 'query', 'info': 'Search query', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'search_depth', 'display_name': 'search depth', 'info': "The depth of the search. It can be 'basic' or 'advanced'", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'basic', 'options': ['basic', 'advanced'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'topic', 'display_name': 'topic', 'info': 'The category of the search. This will determine which of our agents will be used for the search', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'general', 'options': ['general', 'news'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'days', 'display_name': 'days', 'info': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 3, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'time_range', 'display_name': 'time range', 'info': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'max_results', 'display_name': 'max results', 'info': 'The maximum number of search results to return', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 10, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_images', 'display_name': 'include images', 'info': 'Include a list of query-related images in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_image_descriptions', 'display_name': 'include image descriptions', 'info': 'Include a list of query-related images and their descriptions in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_raw_content', 'display_name': 'include raw content', 'info': 'Include the cleaned and parsed HTML content of each search result', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_domains', 'display_name': 'include domains', 'info': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'exclude_domains', 'display_name': 'exclude domains', 'info': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'Generated_String', 'display_name': 'Generated_String', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_tavily_web_search_and_extraction_server_tavily-search', 'type': 'MCP', 'mcp_info': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'properties': {'Generated_String': {'type': 'string', 'description': 'generated string from tavily', 'title': 'Generated_String'}}}}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 426, 'selected': False, 'positionAbsolute': {'x': -160, 'y': -200}, 'dragging': False}, {'id': 'MCP_Script_Generation_script_generate-1750062817323', 'type': 'WorkflowNode', 'position': {'x': -100, 'y': -440}, 'data': {'label': 'Script Generation - script_generate', 'type': 'mcp', 'originalType': 'MCP_Script_Generation_script_generate', 'definition': {'name': 'MCP_Script_Generation_script_generate', 'display_name': 'Script Generation - script_generate', 'description': 'Provide topic and keyword to generator Script', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'topic', 'display_name': 'Topic', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'script_type', 'display_name': 'script type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'TOPIC', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'keywords', 'display_name': 'keywords', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'video_type', 'display_name': 'video type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'SHORT', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'link', 'display_name': 'Link', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'title', 'display_name': 'Title', 'output_type': 'string'}, {'name': 'script', 'display_name': 'Script', 'output_type': 'string'}, {'name': 'script_type', 'display_name': 'Script Type', 'output_type': 'string'}, {'name': 'video_type', 'display_name': 'Video Type', 'output_type': 'string'}, {'name': 'link', 'display_name': 'Link', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_script_generation_script_generate', 'type': 'MCP', 'mcp_info': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 234, 'selected': False, 'positionAbsolute': {'x': -100, 'y': -440}, 'dragging': False}, {'id': 'MCP_Script_Generation_script_generate-1750064278686', 'type': 'WorkflowNode', 'position': {'x': 220, 'y': -40}, 'data': {'label': 'Script Generation - script_generate', 'type': 'mcp', 'originalType': 'MCP_Script_Generation_script_generate', 'definition': {'name': 'MCP_Script_Generation_script_generate', 'display_name': 'Script Generation - script_generate', 'description': 'Provide topic and keyword to generator Script', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'topic', 'display_name': 'Topic', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'script_type', 'display_name': 'script type', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'TOPIC', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'keywords', 'display_name': 'keywords', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'video_type', 'display_name': 'video type', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'SHORT', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'link', 'display_name': 'Link', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'title', 'display_name': 'Title', 'output_type': 'string'}, {'name': 'script', 'display_name': 'Script', 'output_type': 'string'}, {'name': 'script_type', 'display_name': 'Script Type', 'output_type': 'string'}, {'name': 'video_type', 'display_name': 'Video Type', 'output_type': 'string'}, {'name': 'link', 'display_name': 'Link', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_script_generation_script_generate', 'type': 'MCP', 'mcp_info': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}, 'config': {'script_type': 'TOPIC', 'video_type': 'SHORT', 'keywords': '{"time":"new}"'}}, 'style': {'opacity': 1}, 'width': 210, 'height': 234, 'selected': True, 'positionAbsolute': {'x': 220, 'y': -40}, 'dragging': False}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423', 'sourceHandle': 'Generated_String', 'target': 'AgenticAI-1750062782493', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423Generated_String-AgenticAI-1750062782493tools', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Script_Generation_script_generate-1750062817323', 'sourceHandle': 'title', 'target': 'AgenticAI-1750062782493', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750062817323title-AgenticAI-1750062782493tools', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MCP_Script_Generation_script_generate-1750064278686', 'targetHandle': 'topic', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-MCP_Script_Generation_script_generate-1750064278686topic', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Script_Generation_script_generate-1750064278686', 'sourceHandle': 'title', 'target': 'AgenticAI-1750062782493', 'targetHandle': 'query', 'type': 'default', 'id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750064278686title-AgenticAI-1750062782493query'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/2e0088f1-5942-42a7-9d36-32cc5b5660cf.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/2e0088f1-5942-42a7-9d36-32cc5b5660cf.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 5
[DEBUG] Number of edges: 4
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=AgenticAI-1750062782493, type=agent, originalType=AgenticAI
[DEBUG] Node 2: id=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423, type=mcp, originalType=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search
[DEBUG] Node 3: id=MCP_Script_Generation_script_generate-1750062817323, type=mcp, originalType=MCP_Script_Generation_script_generate
[DEBUG] Node 4: id=MCP_Script_Generation_script_generate-1750064278686, type=mcp, originalType=MCP_Script_Generation_script_generate
[DEBUG] Edge 0: id=reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423Generated_String-AgenticAI-1750062782493tools, source=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423, target=AgenticAI-1750062782493, sourceHandle=Generated_String
[DEBUG] Edge 1: id=reactflow__edge-MCP_Script_Generation_script_generate-1750062817323title-AgenticAI-1750062782493tools, source=MCP_Script_Generation_script_generate-1750062817323, target=AgenticAI-1750062782493, sourceHandle=title
[DEBUG] Edge 2: id=reactflow__edge-start-nodeflow-MCP_Script_Generation_script_generate-1750064278686topic, source=start-node, target=MCP_Script_Generation_script_generate-1750064278686, sourceHandle=flow
[DEBUG] Edge 3: id=reactflow__edge-MCP_Script_Generation_script_generate-1750064278686title-AgenticAI-1750062782493query, source=MCP_Script_Generation_script_generate-1750064278686, target=AgenticAI-1750062782493, sourceHandle=title

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 5
   - Edges: 4
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ✅ Found tool node MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423 as separate visual node
   ✅ Tool nodes are already saved as separate visual components
   ✅ Skipping virtual node creation to avoid duplicates
   ✅ Using existing tool nodes and edges
   🔧 CONFIGURING TOOL DATA FLOW FOR EXISTING NODES...
         - Configured 2 tools for AgenticAI node AgenticAI-1750062782493
   ✅ Tool data flow configured for existing tool nodes
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(AgenticAI-1750062782493): node_type='agent', original_type='AgenticAI', result=False
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750062817323): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750064278686): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - AgenticAI: 1
   - MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search: 1
   - MCP_Script_Generation_script_generate: 2
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
   ℹ️  Allowing multiple connections to AgenticAI-1750062782493.tools (tools handle accepts multiple connections)
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Script_Generation_script_generate-1750064278686
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 4
   - Edge mappings: 4
   - All nodes: 5
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 4 nodes
   - Grouped into 2 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 2 tool nodes that will be integrated into agents
         - Agent AgenticAI-1750062782493: 2 tools
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...
      🔧 Integrated 2 tools into agent AgenticAI-1750062782493
         1. Tavily Web Search and Extraction Server - tavily-search (MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search)
         2. Script Generation - script_generate (MCP_Script_Generation_script_generate)

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/5: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/5: AgenticAI-1750062782493
   Type: AgenticAI (agent)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(AgenticAI-1750062782493): node_type='agent', original_type='AgenticAI', result=False
         ⏭️  SKIPPED: Tool input field (tools should be callable functions, not input sources)
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/5: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423
   Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
   ⏭️  SKIPPED: Tool node (integrated into agent configuration)

📦 Processing node 4/5: MCP_Script_Generation_script_generate-1750062817323
   Type: MCP_Script_Generation_script_generate (mcp)
   ⏭️  SKIPPED: Tool node (integrated into agent configuration)

📦 Processing node 5/5: MCP_Script_Generation_script_generate-1750064278686
   Type: MCP_Script_Generation_script_generate (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750064278686): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
   🔧 Fixed MCP tool_name: script_generate
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (2 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 2 ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423', 'MCP_Script_Generation_script_generate-1750062817323']
   - Component nodes processed: 2 ['AgenticAI-1750062782493', 'MCP_Script_Generation_script_generate-1750064278686']
   - Final transition_nodes count: 2
   - Agent tool integrations: 1 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Script_Generation_script_generate-1750062817323', 'MCP_Script_Generation_script_generate-1750064278686', 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423']

   📦 Processing node: MCP_Script_Generation_script_generate-1750062817323
      Type: MCP_Script_Generation_script_generate (mcp)
      ⏭️  SKIPPED: Tool node (integrated into agent configuration)

   📦 Processing node: MCP_Script_Generation_script_generate-1750064278686
      Type: MCP_Script_Generation_script_generate (mcp)
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750064278686): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Script_Generation_script_generate-1750064278686): node_type='mcp', result=False
[DEBUG] is_conditional_node(AgenticAI-1750062782493): node_type='agent', original_type='AgenticAI', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Script_Generation_script_generate-1750064278686
         - Sequence: 1
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

   📦 Processing node: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423
      Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
      ⏭️  SKIPPED: Tool node (integrated into agent configuration)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['AgenticAI-1750062782493']

   📦 Processing node: AgenticAI-1750062782493
      Type: AgenticAI (agent)
[DEBUG] is_conditional_node(AgenticAI-1750062782493): node_type='agent', original_type='AgenticAI', result=False
      Is conditional: False
[DEBUG] is_output_node(AgenticAI-1750062782493): node_type='agent', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 2
   - Total transitions created: 2
   - Conditional transitions: 0
   - Regular transitions: 1

⚙️  REGULAR TRANSITIONS CREATED:
   - MCP_Script_Generation_script_generate-1750064278686: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'AgenticAI', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'input_schema': {'predefined_fields': [{'field_name': 'model_provider', 'data_type': {'type': 'string', 'description': 'The AI model provider to use.'}, 'required': False}, {'field_name': 'base_url', 'data_type': {'type': 'string', 'description': 'Base URL for the API (leave empty for default provider URL).'}, 'required': False}, {'field_name': 'api_key', 'data_type': {'type': 'string', 'description': 'API key for the model provider. Can be entered directly or referenced from secure storage.'}, 'required': False}, {'field_name': 'model_name', 'data_type': {'type': 'string', 'description': 'Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.'}, 'required': False}, {'field_name': 'temperature', 'data_type': {'type': 'number', 'description': 'Controls randomness: 0 is deterministic, higher values are more random.'}, 'required': False}, {'field_name': 'description', 'data_type': {'type': 'string', 'description': 'Description of the agent for UI display.'}, 'required': False}, {'field_name': 'execution_type', 'data_type': {'type': 'string', 'description': 'Determines if agent handles single response or multi-turn conversation.'}, 'required': False}, {'field_name': 'query', 'data_type': {'type': 'string', 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, 'required': True}, {'field_name': 'system_message', 'data_type': {'type': 'string', 'description': 'System prompt/instructions for the agent. If empty, will use default based on query.'}, 'required': False}, {'field_name': 'termination_condition', 'data_type': {'type': 'string', 'description': 'Defines when multi-turn conversations should end. Required for interactive execution type.'}, 'required': False}, {'field_name': 'max_tokens', 'data_type': {'type': 'number', 'description': 'Maximum response length in tokens.'}, 'required': False}, {'field_name': 'input_variables', 'data_type': {'type': 'object', 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'memory', 'data_type': {'type': 'string', 'description': 'Connect a memory object from another node.'}, 'required': False}, {'field_name': 'autogen_agent_type', 'data_type': {'type': 'string', 'description': 'The type of AutoGen agent to create internally.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'final_answer', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'intermediate_steps', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'updated_memory', 'data_type': {'type': 'string', 'description': '', 'format': 'datetime'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}, {'id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'script_generate', 'input_schema': {'predefined_fields': [{'field_name': 'topic', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'script_type', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'keywords', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'video_type', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'link', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'title', 'data_type': {'type': 'string', 'description': 'Title of the generated script', 'format': 'string'}}, {'field_name': 'script', 'data_type': {'type': 'string', 'description': 'The generated script', 'format': 'string'}}, {'field_name': 'script_type', 'data_type': {'type': 'string', 'description': 'Type of the script', 'format': 'string'}}, {'field_name': 'video_type', 'data_type': {'type': 'string', 'description': 'The type of video', 'format': 'video'}}, {'field_name': 'link', 'data_type': {'type': 'string', 'description': 'Optional link for the script', 'format': 'uri'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Script_Generation_script_generate-1750064278686', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_info': {'node_id': '0dc83245-794f-405d-8814-7771260d3c60', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'script_generate', 'tool_params': {'items': [{'field_name': 'topic', 'data_type': 'string', 'field_value': None}, {'field_name': 'script_type', 'data_type': 'string', 'field_value': 'TOPIC'}, {'field_name': 'keywords', 'data_type': 'string', 'field_value': '{"time":"new}"'}, {'field_name': 'video_type', 'data_type': 'string', 'field_value': 'SHORT'}, {'field_name': 'link', 'data_type': 'string', 'field_value': None}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-AgenticAI-1750062782493', 'target_node_id': 'AI Agent Executor', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'title', 'result_path': 'title', 'edge_id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750064278686title-AgenticAI-1750062782493query'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'topic', 'handle_name': 'Topic', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'script_type', 'handle_name': 'script type', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'keywords', 'handle_name': 'keywords', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'video_type', 'handle_name': 'video type', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'link', 'handle_name': 'Link', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'title', 'handle_name': 'Title', 'data_type': 'string', 'description': ''}, {'handle_id': 'script', 'handle_name': 'Script', 'data_type': 'string', 'description': ''}, {'handle_id': 'script_type', 'handle_name': 'Script Type', 'data_type': 'string', 'description': ''}, {'handle_id': 'video_type', 'handle_name': 'Video Type', 'data_type': 'string', 'description': ''}, {'handle_id': 'link', 'handle_name': 'Link', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'title': 'title', 'script': 'script', 'script_type': 'script_type', 'video_type': 'video_type', 'link': 'link'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.title', 'output_data.title', 'response.title', 'data.title', 'result.script', 'output_data.script', 'response.script', 'data.script', 'result.script_type', 'output_data.script_type', 'response.script_type', 'data.script_type', 'result.video_type', 'output_data.video_type', 'response.video_type', 'data.video_type', 'result.link', 'output_data.link', 'response.link', 'data.link', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'title'}}, 'approval_required': False, 'end': False}, {'id': 'transition-AgenticAI-1750062782493', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'agent', 'node_info': {'node_id': 'AgenticAI', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'tool_params': {'items': [{'field_name': 'agent_type', 'data_type': 'string', 'field_value': 'component'}, {'field_name': 'execution_type', 'data_type': 'string', 'field_value': 'response'}, {'field_name': 'query', 'data_type': 'string', 'field_value': None}, {'field_name': 'agent_config', 'data_type': 'object', 'field_value': {'agent_tools': [{'tool_type': 'workflow_component', 'component': {'component_id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423', 'component_type': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'component_name': 'Tavily Web Search and Extraction Server - tavily-search', 'component_description': "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'properties': {'Generated_String': {'type': 'string', 'description': 'generated string from tavily', 'title': 'Generated_String'}}}, 'mcp_metadata': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'tool_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}}}}, {'tool_type': 'workflow_component', 'component': {'component_id': 'MCP_Script_Generation_script_generate-1750062817323', 'component_type': 'MCP_Script_Generation_script_generate', 'component_name': 'Script Generation - script_generate', 'component_description': 'Provide topic and keyword to generator Script', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}, 'mcp_metadata': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'tool_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}}}}]}}]}}], 'input_data': [{'from_transition_id': 'transition-MCP_Script_Generation_script_generate-1750064278686', 'source_node_id': '0dc83245-794f-405d-8814-7771260d3c60', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Script_Generation_script_generate-1750064278686', 'source_handle_id': 'title', 'target_handle_id': 'query', 'edge_id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750064278686title-AgenticAI-1750062782493query'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'agent', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'Query/Objective', 'data_type': 'string', 'required': True, 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, {'handle_id': 'input_variables', 'handle_name': 'Input Variables', 'data_type': 'object', 'required': False, 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, {'handle_id': 'tools', 'handle_name': 'Tools', 'data_type': 'string', 'required': False, 'description': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.'}, {'handle_id': 'memory', 'handle_name': 'Memory Object', 'data_type': 'string', 'required': False, 'description': 'Connect a memory object from another node.'}], 'output_handles': [{'handle_id': 'final_answer', 'handle_name': 'Final Answer', 'data_type': 'string', 'description': ''}, {'handle_id': 'intermediate_steps', 'handle_name': 'Intermediate Steps', 'data_type': 'string', 'description': ''}, {'handle_id': 'updated_memory', 'handle_name': 'Updated Memory', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'final_answer': 'final_answer', 'intermediate_steps': 'intermediate_steps', 'updated_memory': 'updated_memory', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.final_answer', 'output_data.final_answer', 'response.final_answer', 'data.final_answer', 'result.intermediate_steps', 'output_data.intermediate_steps', 'response.intermediate_steps', 'data.intermediate_steps', 'result.updated_memory', 'output_data.updated_memory', 'response.updated_memory', 'data.updated_memory', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'final_answer'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/10980659-c3bf-4519-a034-c3f08a550613.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/10980659-c3bf-4519-a034-c3f08a550613.json
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-06-16 14:28:41 [info     ] Set is_updated=True for workflow 98373bf1-ddb1-4cb4-ad5f-94ec56987fef due to version-relevant changes
2025-06-16 14:28:42 [info     ] Checking derived workflow update conditions: template_relevant_fields_changed=True, workflow.visibility=WorkflowVisibilityEnum.PRIVATE, is_public=False
2025-06-16 14:28:42 [info     ] Skipping derived workflow update for workflow 98373bf1-ddb1-4cb4-ad5f-94ec56987fef
2025-06-16 14:43:24 - workflow-service - INFO - discoverComponents request received
2025-06-16 14:43:24 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'WebhookComponent' in category 'Data Interaction' registered successfully
DEBUG: Component WebhookComponent registered successfully
DEBUG: Component 'DocExtractorComponent' in category 'Helpers' registered successfully
DEBUG: Component DocExtractorComponent registered successfully
DEBUG: Component 'IDGeneratorComponent' in category 'Helpers' registered successfully
DEBUG: Component IDGeneratorComponent registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
DEBUG: Component 'AlterMetadataComponent' in category 'Processing' registered successfully
DEBUG: Component AlterMetadataComponent registered successfully
DEBUG: Component 'CombineTextComponent' in category 'Processing' registered successfully
DEBUG: Component CombineTextComponent registered successfully
DEBUG: Component 'DataToDataFrameComponent' in category 'Processing' registered successfully
DEBUG: Component DataToDataFrameComponent registered successfully
DEBUG: Component 'MergeDataComponent' in category 'Processing' registered successfully
DEBUG: Component MergeDataComponent registered successfully
DEBUG: Component 'MessageToDataComponent' in category 'Processing' registered successfully
DEBUG: Component MessageToDataComponent registered successfully
DEBUG: Component 'SaveToFileComponent' in category 'Processing' registered successfully
DEBUG: Component SaveToFileComponent registered successfully
DEBUG: Component 'SelectDataComponent' in category 'Processing' registered successfully
DEBUG: Component SelectDataComponent registered successfully
DEBUG: Component 'SplitTextComponent' in category 'Processing' registered successfully
DEBUG: Component SplitTextComponent registered successfully
DEBUG: Component 'MCPToolsComponent' in category 'Tools' registered successfully
DEBUG: Component MCPToolsComponent registered successfully
2025-06-16 14:43:24 [info     ] Components discovered successfully count=23
2025-06-16 14:43:24 - workflow-service - INFO - discoverComponents request received
2025-06-16 14:43:24 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'WebhookComponent' in category 'Data Interaction' registered successfully
DEBUG: Component WebhookComponent registered successfully
DEBUG: Component 'DocExtractorComponent' in category 'Helpers' registered successfully
DEBUG: Component DocExtractorComponent registered successfully
DEBUG: Component 'IDGeneratorComponent' in category 'Helpers' registered successfully
DEBUG: Component IDGeneratorComponent registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
DEBUG: Component 'AlterMetadataComponent' in category 'Processing' registered successfully
DEBUG: Component AlterMetadataComponent registered successfully
DEBUG: Component 'CombineTextComponent' in category 'Processing' registered successfully
DEBUG: Component CombineTextComponent registered successfully
DEBUG: Component 'DataToDataFrameComponent' in category 'Processing' registered successfully
DEBUG: Component DataToDataFrameComponent registered successfully
DEBUG: Component 'MergeDataComponent' in category 'Processing' registered successfully
DEBUG: Component MergeDataComponent registered successfully
DEBUG: Component 'MessageToDataComponent' in category 'Processing' registered successfully
DEBUG: Component MessageToDataComponent registered successfully
DEBUG: Component 'SaveToFileComponent' in category 'Processing' registered successfully
DEBUG: Component SaveToFileComponent registered successfully
DEBUG: Component 'SelectDataComponent' in category 'Processing' registered successfully
DEBUG: Component SelectDataComponent registered successfully
DEBUG: Component 'SplitTextComponent' in category 'Processing' registered successfully
DEBUG: Component SplitTextComponent registered successfully
DEBUG: Component 'MCPToolsComponent' in category 'Tools' registered successfully
DEBUG: Component MCPToolsComponent registered successfully
2025-06-16 14:43:24 [info     ] Components discovered successfully count=23
2025-06-16 14:43:32 - workflow-service - INFO - discoverComponents request received
2025-06-16 14:43:32 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'WebhookComponent' in category 'Data Interaction' registered successfully
DEBUG: Component WebhookComponent registered successfully
DEBUG: Component 'DocExtractorComponent' in category 'Helpers' registered successfully
DEBUG: Component DocExtractorComponent registered successfully
DEBUG: Component 'IDGeneratorComponent' in category 'Helpers' registered successfully
DEBUG: Component IDGeneratorComponent registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
DEBUG: Component 'AlterMetadataComponent' in category 'Processing' registered successfully
DEBUG: Component AlterMetadataComponent registered successfully
DEBUG: Component 'CombineTextComponent' in category 'Processing' registered successfully
DEBUG: Component CombineTextComponent registered successfully
DEBUG: Component 'DataToDataFrameComponent' in category 'Processing' registered successfully
DEBUG: Component DataToDataFrameComponent registered successfully
DEBUG: Component 'MergeDataComponent' in category 'Processing' registered successfully
DEBUG: Component MergeDataComponent registered successfully
DEBUG: Component 'MessageToDataComponent' in category 'Processing' registered successfully
DEBUG: Component MessageToDataComponent registered successfully
DEBUG: Component 'SaveToFileComponent' in category 'Processing' registered successfully
DEBUG: Component SaveToFileComponent registered successfully
DEBUG: Component 'SelectDataComponent' in category 'Processing' registered successfully
DEBUG: Component SelectDataComponent registered successfully
DEBUG: Component 'SplitTextComponent' in category 'Processing' registered successfully
DEBUG: Component SplitTextComponent registered successfully
DEBUG: Component 'MCPToolsComponent' in category 'Tools' registered successfully
DEBUG: Component MCPToolsComponent registered successfully
2025-06-16 14:43:32 [info     ] Components discovered successfully count=23
2025-06-16 14:43:32 - workflow-service - INFO - discoverComponents request received
2025-06-16 14:43:32 [info     ] discoverComponents request received force_refresh=True
DEBUG: Component 'AgenticAI' in category 'AI' registered successfully
DEBUG: Component AgenticAI registered successfully
DEBUG: Component 'BasicLLMChain' in category 'AI' registered successfully
DEBUG: Component BasicLLMChain registered successfully
DEBUG: Component 'Classifier' in category 'AI' registered successfully
DEBUG: Component Classifier registered successfully
DEBUG: Component 'InformationExtractor' in category 'AI' registered successfully
DEBUG: Component InformationExtractor registered successfully
DEBUG: Component 'QuestionAnswerModule' in category 'AI' registered successfully
DEBUG: Component QuestionAnswerModule registered successfully
DEBUG: Component 'SentimentAnalyzer' in category 'AI' registered successfully
DEBUG: Component SentimentAnalyzer registered successfully
DEBUG: Component 'Summarizer' in category 'AI' registered successfully
DEBUG: Component Summarizer registered successfully
DEBUG: Component 'ConditionalNode' in category 'Logic' registered successfully
DEBUG: Component ConditionalNode registered successfully
DEBUG: Component 'LoopNode' in category 'Logic' registered successfully
DEBUG: Component LoopNode registered successfully
DEBUG: Component 'ApiRequestNode' in category 'Data Interaction' registered successfully
DEBUG: Component ApiRequestNode registered successfully
DEBUG: Component 'WebhookComponent' in category 'Data Interaction' registered successfully
DEBUG: Component WebhookComponent registered successfully
DEBUG: Component 'DocExtractorComponent' in category 'Helpers' registered successfully
DEBUG: Component DocExtractorComponent registered successfully
DEBUG: Component 'IDGeneratorComponent' in category 'Helpers' registered successfully
DEBUG: Component IDGeneratorComponent registered successfully
DEBUG: Component 'StartNode' in category 'IO' registered successfully
DEBUG: Component StartNode registered successfully
DEBUG: Component 'AlterMetadataComponent' in category 'Processing' registered successfully
DEBUG: Component AlterMetadataComponent registered successfully
DEBUG: Component 'CombineTextComponent' in category 'Processing' registered successfully
DEBUG: Component CombineTextComponent registered successfully
DEBUG: Component 'DataToDataFrameComponent' in category 'Processing' registered successfully
DEBUG: Component DataToDataFrameComponent registered successfully
DEBUG: Component 'MergeDataComponent' in category 'Processing' registered successfully
DEBUG: Component MergeDataComponent registered successfully
DEBUG: Component 'MessageToDataComponent' in category 'Processing' registered successfully
DEBUG: Component MessageToDataComponent registered successfully
DEBUG: Component 'SaveToFileComponent' in category 'Processing' registered successfully
DEBUG: Component SaveToFileComponent registered successfully
DEBUG: Component 'SelectDataComponent' in category 'Processing' registered successfully
DEBUG: Component SelectDataComponent registered successfully
DEBUG: Component 'SplitTextComponent' in category 'Processing' registered successfully
DEBUG: Component SplitTextComponent registered successfully
DEBUG: Component 'MCPToolsComponent' in category 'Tools' registered successfully
DEBUG: Component MCPToolsComponent registered successfully
2025-06-16 14:43:33 [info     ] Components discovered successfully count=23
2025-06-16 14:43:51 - workflow-service - INFO - updateWorkflow request received
2025-06-16 14:43:51 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=eccfb0c1-0ce6-4545-8917-3e5e85317fcb
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': 100, 'y': 100}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'MCP_duckduckgo-mcp-server_search-1750065224642_query': {'node_id': 'MCP_duckduckgo-mcp-server_search-1750065224642', 'node_name': 'duckduckgo-mcp-server - search', 'input_name': 'query', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}}}}, 'width': 208, 'height': 122, 'selected': False, 'dragging': False}, {'id': 'MCP_duckduckgo-mcp-server_search-1750065224642', 'type': 'WorkflowNode', 'position': {'x': 480, 'y': -20}, 'data': {'label': 'duckduckgo-mcp-server - search', 'type': 'mcp', 'originalType': 'MCP_duckduckgo-mcp-server_search', 'definition': {'name': 'MCP_duckduckgo-mcp-server_search', 'display_name': 'duckduckgo-mcp-server - search', 'description': '\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'query', 'display_name': 'Query', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'max_results', 'display_name': 'Max Results', 'info': '', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 10, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'test', 'display_name': 'test', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_duckduckgo-mcp-server_search', 'type': 'MCP', 'mcp_info': {'server_id': '7e60955e-d7bb-4913-b10b-0806af48fbd6', 'server_path': '', 'tool_name': 'search', 'input_schema': {'properties': {'query': {'title': 'Query', 'type': 'string'}, 'max_results': {'default': 10, 'title': 'Max Results', 'type': 'integer'}}, 'required': ['query'], 'title': 'searchArguments', 'type': 'object'}, 'output_schema': {'properties': {'test': {'type': 'string', 'description': 'test', 'title': 'test'}}}}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 150, 'selected': False, 'positionAbsolute': {'x': 480, 'y': -20}, 'dragging': False}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MCP_duckduckgo-mcp-server_search-1750065224642', 'targetHandle': 'query', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-MCP_duckduckgo-mcp-server_search-1750065224642query'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/3178da61-fdcd-4a06-9328-97bbf2358a92.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/3178da61-fdcd-4a06-9328-97bbf2358a92.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 2
[DEBUG] Number of edges: 1
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=MCP_duckduckgo-mcp-server_search-1750065224642, type=mcp, originalType=MCP_duckduckgo-mcp-server_search
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-MCP_duckduckgo-mcp-server_search-1750065224642query, source=start-node, target=MCP_duckduckgo-mcp-server_search-1750065224642, sourceHandle=flow

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 2
   - Edges: 1
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(MCP_duckduckgo-mcp-server_search-1750065224642): node_type='mcp', original_type='MCP_duckduckgo-mcp-server_search', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - MCP_duckduckgo-mcp-server_search: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_duckduckgo-mcp-server_search-1750065224642
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - Edge mappings: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/2: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/2: MCP_duckduckgo-mcp-server_search-1750065224642
   Type: MCP_duckduckgo-mcp-server_search (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_duckduckgo-mcp-server_search-1750065224642): node_type='mcp', original_type='MCP_duckduckgo-mcp-server_search', result=False
   🔧 Fixed MCP tool_name: search
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (1 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 1 ['MCP_duckduckgo-mcp-server_search-1750065224642']
   - Final transition_nodes count: 1
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_duckduckgo-mcp-server_search-1750065224642']

   📦 Processing node: MCP_duckduckgo-mcp-server_search-1750065224642
      Type: MCP_duckduckgo-mcp-server_search (mcp)
[DEBUG] is_conditional_node(MCP_duckduckgo-mcp-server_search-1750065224642): node_type='mcp', original_type='MCP_duckduckgo-mcp-server_search', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_duckduckgo-mcp-server_search-1750065224642): node_type='mcp', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 1
   - Total transitions created: 1
   - Conditional transitions: 0
   - Regular transitions: 0

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
❌ Validation Errors in transition schema:
- Path: nodes/0/server_tools/0/input_schema/predefined_fields/1/data_type/type
  Message: 'integer' is not one of ['string', 'number', 'boolean', 'array', 'object']
[DEBUG] Transition schema validation failed for PATCH: Transition schema validation failed.
