import pytest
import json
from unittest.mock import patch, Mock, AsyncMock
from app.execution.executor_server_kafka import KafkaWorkflowConsumer


class TestExecutorServerKafkaIntegration:
    """
    Integration tests for the KafkaWorkflowConsumer class.
    """

    @pytest.fixture
    def mock_consumer(self):
        """
        Provides a mock KafkaWorkflowConsumer with mocked dependencies.
        """
        with patch("app.execution.executor_server_kafka.AIOKafkaConsumer"), patch(
            "app.execution.executor_server_kafka.AIOKafkaProducer"
        ), patch("app.execution.executor_server_kafka.KafkaToolExecutor"), patch(
            "app.execution.executor_server_kafka.NodeExecutor"
        ):
            consumer = KafkaWorkflowConsumer()
            consumer.send_response = AsyncMock()
            consumer.send_error_response = AsyncMock()
            consumer.consumer = Mock()
            consumer.consumer.commit = AsyncMock()
            return consumer

    @pytest.fixture
    def mock_message(self):
        """
        Provides a mock Kafka message.
        """
        message = Mock()
        message.value = json.dumps(
            {
                "data": {
                    "workflow_id": "test-workflow",
                    "payload": {
                        "user_dependent_fields": [],
                        "user_payload_template": {},
                    },
                }
            }
        ).encode("utf-8")
        message.headers = [
            (b"correlationId", b"test-correlation-id"),
            (b"reply-topic", b"test-reply-topic"),
        ]
        message.topic = "workflow-requests"
        return message

    @pytest.mark.asyncio
    async def test_process_workflow_request_modern_execution(
        self, mock_consumer, mock_message
    ):
        """
        Test that process_workflow_request works with modern MCP execution (no URL processing needed).
        """
        # Mock the fetch_workflow_orchestration function
        sample_workflow = {
            "nodes": [{"id": "node1", "server_tools": []}],
            "transitions": [
                {
                    "id": "transition1",
                    "sequence": 1,
                    "transition_type": "initial",
                    "execution_type": "MCP",
                    "node_info": {"node_id": "node1"},
                    "end": False,
                }
            ],
        }

        with patch(
            "app.execution.executor_server_kafka.fetch_workflow_orchestration",
            return_value=sample_workflow,
        ), patch(
            "app.execution.executor_server_kafka.initialize_workflow_with_params",
            return_value=sample_workflow,
        ), patch(
            "app.execution.executor_server_kafka.EnhancedWorkflowEngine"
        ) as mock_engine_class, patch(
            "asyncio.create_task"
        ):

            # Mock the engine instance
            mock_engine = Mock()
            mock_engine.execute = AsyncMock(return_value=True)
            mock_engine_class.return_value = mock_engine

            # Call the method under test
            result = await mock_consumer.process_workflow_request(
                mock_message.value, mock_message
            )

            # Verify the result
            assert result is True

            # Verify that the engine was initialized with the workflow (no URL processing)
            mock_engine_class.assert_called_once()
            init_workflow_arg = mock_engine_class.call_args[1]["init_workflow"]
            assert init_workflow_arg == sample_workflow
